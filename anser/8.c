#include <stdio.h>

int n;
int a[10][10];
int result = 0;

// 检查选手1是否能在给定的分组中获胜
int can_win(int groups[][2], int num_groups) {
    // 模拟比赛过程
    int winners[10];
    int winner_count = 0;
    
    // 第一轮比赛
    for (int i = 0; i < num_groups; i++) {
        int p1 = groups[i][0];
        int p2 = groups[i][1];
        
        if (a[p1][p2] == 1) {
            winners[winner_count++] = p1;
        } else {
            winners[winner_count++] = p2;
        }
    }
    
    // 继续比赛直到只剩一个人
    while (winner_count > 1) {
        int new_winners[10];
        int new_count = 0;
        
        for (int i = 0; i < winner_count; i += 2) {
            int p1 = winners[i];
            int p2 = winners[i + 1];
            
            if (a[p1][p2] == 1) {
                new_winners[new_count++] = p1;
            } else {
                new_winners[new_count++] = p2;
            }
        }
        
        winner_count = new_count;
        for (int i = 0; i < winner_count; i++) {
            winners[i] = new_winners[i];
        }
    }
    
    return winners[0] == 1;
}

// 生成所有可能的分组
void generate_groups(int players[], int used[], int groups[][2], int group_count, int player_count) {
    if (player_count == n) {
        // 所有选手都已分组，检查选手1是否能获胜
        if (can_win(groups, n / 2)) {
            result++;
        }
        return;
    }
    
    // 找到第一个未分组的选手
    int first = -1;
    for (int i = 1; i <= n; i++) {
        if (!used[i]) {
            first = i;
            break;
        }
    }
    
    if (first == -1) return;
    
    used[first] = 1;
    
    // 尝试与其他未分组的选手配对
    for (int i = first + 1; i <= n; i++) {
        if (!used[i]) {
            used[i] = 1;
            groups[group_count][0] = first;
            groups[group_count][1] = i;
            
            generate_groups(players, used, groups, group_count + 1, player_count + 2);
            
            used[i] = 0;
        }
    }
    
    used[first] = 0;
}

int main() {
    scanf("%d", &n);
    
    for (int i = 1; i <= n; i++) {
        for (int j = 1; j <= n; j++) {
            scanf("%d", &a[i][j]);
        }
    }
    
    int players[10];
    int used[10] = {0};
    int groups[10][2];
    
    for (int i = 1; i <= n; i++) {
        players[i-1] = i;
    }
    
    generate_groups(players, used, groups, 0, 0);
    
    printf("%d\n", result);
    return 0;
}
