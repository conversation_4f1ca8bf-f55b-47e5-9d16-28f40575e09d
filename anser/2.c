#include <stdio.h>
#include <string.h>

int main() {
    int T;
    scanf("%d", &T);
    
    while (T--) {
        char time_str[10];
        int x;
        scanf("%s %d", time_str, &x);
        
        // 解析时间字符串 AB:CD:EF
        int hours = (time_str[0] - '0') * 10 + (time_str[1] - '0');
        int minutes = (time_str[3] - '0') * 10 + (time_str[4] - '0');
        int seconds = (time_str[6] - '0') * 10 + (time_str[7] - '0');
        
        // 计算总秒数
        long long total_seconds = hours * 3600LL + minutes * 60LL + seconds;
        
        // 计算总帧数
        long long total_frames = total_seconds * x;
        
        printf("%lld\n", total_frames);
    }
    
    return 0;
}
