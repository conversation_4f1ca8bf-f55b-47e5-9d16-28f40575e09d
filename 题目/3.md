# 清华考研机试 2024 调剂
# U423276 金色传说

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

由于天梯上一直被快攻打烂上不去传说，Xf 最近一直特别懊恼。他觉得是自己的传说牌不够多，所以很想开出多几份高质量的金色传说卡牌。

Xf 来找到酒店老板，酒店老板展示了一对琳琅满目的卡牌包让他目不暇接。酒店老板告诉他，第 $i$ 号卡牌包有一定的价值 $a_i$ ，也有一定的价格 $b_i$ ，Xf 想要挑选其中的 $k$ 个卡牌包。但是卡牌包的购买方式比较特别，Xf 排列他们的方式影响了购买的价格。

Xf 挑选好 $k$ 个卡牌包，并且按照自己喜欢的任意方式依次排开。设 Xf 的开包的卡牌包编号按开包顺序为 $c_1,...,c_k$，则它们的价值总和为 $\sum\limits_{i=1}^k a_{c_i}$，而购买的价格是 $\sum\limits_{i=1}^{k-1} |b_{c_i}-b_{c_{i+1}}|$ 。Xf 获得的受益是价值总和减去购买价格，现在 Xf 想知道他能够获得的最大收益是什么，即最大化：

$$
\sum\limits_{i=1}^k a_{c_i}-\sum\limits_{i=1}^{k-1} |b_{c_i}-b_{c_{i+1}}|
$$

你能帮帮他吗？

## 输入格式

从标准输入读入数据。

输入的第一行包含两个整数 $n$ 和 $k$ ，表示卡牌包个数和 Xf 需要选择的卡牌包个数。

接下来输入 $n$ 行，每行包含两个整数 $a_i$ 和 $b_i$ ，表示第 $i$ 个卡牌包的价值和价格。

## 输出格式

输出到标准输出。

输出一个整数，表示 Xf 能够获得的最大收益。

## 输入输出样例 #1

### 输入 #1

```
5 5
1 5
2 4
3 3
4 2
5 1
```

### 输出 #1

```
11
```

## 输入输出样例 #2

### 输入 #2

```
5 2
1 5
2 4
3 3
4 2
5 1
```

### 输出 #2

```
8
```

## 说明/提示

### 样例 1 解释

将五个卡包都买下来，并按照第 5,4,3,2,1 的顺序开包，其价值总和为 $5+4+3+2+1=15$ ，购买价格为 $|1-2|+|2-3|+|3-4|+|4-5|=4$ ，此时的最大收益为 $15-4=11$ 。

### 样例 2 解释

选择购买第 4 和第 5 个卡包，按照任意顺序开包的最大收益均为 $(4+5)-|1-2|=8$ 。

### 数据范围

对于全部的数据，保证 $2\le k\le n\le 5000,1\le a_i,b_i\le 10^9$ 。

| 子任务编号 | 分值 | $n\le$ |  $a_i,b_i\le$  | 其他限制 |
| :----: | :-----: | :-----: | :-----: |:-----: |
|   1| 20    |  $10$   |   $100$   |无  |
|   2| 20   | $100$  |   $10^9$   | 无 |
|   3|20     |  $5000$  |   $10^9$   | $k=2$| 
|  4|20    |  $5000$  |   $100$   | 无|
|   5|20    | $5000$  | $10^9$  |无 |




# U423279 Balatro

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

小瑾最近迷上了一款名为《Balatro》的游戏。

《Balatro》以风靡全球的德州扑克玩法为灵感，玩法简单却非常容易令人上瘾。在每局游戏中，玩家会获得 $n~(n\le 8)$ 张不同的扑克牌作为手牌，每张扑克牌有花色和点数两种属性，其中花色为黑桃（$\texttt{S}$）、红桃（$\texttt{H}$）、梅花（$\texttt{D}$）、方块（$\texttt{C}$），点数从小到大为 $\texttt{23456789XJQKA}$ 。**注意：点数 $10$ 在本题中记为 $\texttt{X}$**。在每次出牌中，玩家可以选择 $1\sim 5$ 张牌打出，出牌后系统将计算得分。

具体地，游戏存在 $9$ 种不同的牌型，每一种牌型对应着不同的基础筹码和倍率，每种牌型的说明如下：

| 牌型名称  | 说明 |  基础筹码  | 倍率 |
| :----:  | :-----: | :-----: |:-----: |
|   同花顺    |  $5$ 张点数连续且花色相同的牌一同打出，全部计分。   |   $100$   | $8$  |
|   四条   | $4$ 张点数相同的牌计分，可以与最多 $1$ 张其他未计分的牌一同打出。  |   $60$   | $7$ |
|  葫芦    |  三条及对子，$3$ 张点数相同的牌与另外 $2$ 张点数相同的牌一同打出，全部计分。  |   $40$   | $4$ | 
|  同花  |  $5$ 张花色相同的牌一同打出，全部计分。  |   $35$  | $4$ |
|  顺子   | $5$ 张点数连续的牌一同打出，全部计分。  | $30$  | $4$ |
|  三条   | $3$ 张点数相同的牌计分，可以与最多 $2$ 张其他未计分的牌一同打出。  | $30$  | $3$ |
|  两对   | $2$ 组不同点数的对子共计 $4$ 张牌计分，可以与最多 $1$ 张其他未计分的牌一同打出。  | $20$  | $2$ |
|  对子   | $2$ 张点数相同的牌计分，可以与最多 $3$ 张其他未计分的牌一同打出。  | $10$  | $2$ |
|  高牌   | 如果打出的牌不满足其他任何牌型，那么只有点数最高的牌计算得分。  | $5$  | $1$ |

如果出牌满足多种牌型，则牌型为从上至下**最先满足**的类型。其中“点数连续”是指在按照游戏规定当中的点数从小到大分布是连续的。特殊地，点数为 $\texttt{A2345}$ 的五张牌不构成顺子，也无法构成同花顺。

系统根据打出的牌确定牌型和参与计分的牌，在确定完基础筹码和倍率后，系统将根据玩家打出的手牌计算最终筹码。玩家打出的每一张**参与计分**的手牌可以增加筹码，点数为 $\texttt{23456789X}$ 的牌增加点数对应数值的筹码，点数为 $\texttt{JQK}$ 的牌可以增加 $10$ 点筹码，点数为 $\texttt{A}$ 的牌增加 $11$ 点筹码。出牌的得分定义为 : 最终筹码×倍率。

小瑾找到了你，请你告诉他在一次出牌中能获得的最高分是多少。

**注意：游戏规则请以本题面描述为准。**

## 输入格式

从标准输入读入数据。

第一行一个整数 $n$ ，表示手牌的数量。

接下来 $n$ 行每行两个字符，用空格隔开，分别表示花色和点数。第一个字符表示花色，$\texttt{S}$ 表示黑桃，$\texttt{H}$ 表示红桃，$\texttt{D}$ 表示方块，$\texttt{C}$ 表示梅花；第二个字符代表点数，为 $\texttt{23456789XJQKA}$ 中的一个。

## 输出格式

输出到标准输出。

输出一个整数，表示在一次出牌中能获得的最高得分。

## 输入输出样例 #1

### 输入 #1

```
3
S 2
H 2
C A
```

### 输出 #1

```
28
```

## 输入输出样例 #2

### 输入 #2

```
8
S X
H X
C X
C 9
D 3
D 7
S 3
S A
```

### 输出 #2

```
304
```

## 说明/提示

### 样例 1 解释

一共有 $3$ 张牌，小瑾可以选择其中的 $1\sim 3$ 张牌可以打出。

仅选择任意 $1$ 张牌打出的话，均为高牌。选择梅花 $\texttt{A}$ 的分数最大，此时的基础筹码为 $5$ ，额外筹码为 $11$ ，倍率为 $1$ 。可以获得的分数为 $(5+11)\times 1=16$ 。

选择其中的 $2$ 张牌打出的话，选择黑桃 $\texttt{2}$ 和红桃 $\texttt{2}$ 共同打出则为对子。此时基础筹码为 $10$ ，额外筹码为两张 $2$ 的点数和，倍率为 $2$ 。可以获得的分数为 $(10+2+2)\times 2=28$ 。

如果 $3$ 张牌全部打出的话，其效果和仅打出一组对子是一致的，因为只有参与计分的两张牌参与到额外筹码的计算。此时的分数同样是 $28$ 。故小瑾最多可以获得 $28$ 分。

### 样例 2 解释

由于至多只能选择 $5$ 张牌，选择第 $1,2,3,5,7$ 张牌打出可以构成葫芦，此时可以获得的分数为 $(40+10+10+10+3+3)\times 4=304$ 。不难得出这是小瑾在所有可打出牌型中可以获得的最大分数。

### 数据范围

对于全部数据，$n\le 8$ ，保证所有手牌中不存在两张花色和点数都相同的牌。

| 测试点编号  | 数据范围  | 其他限制 |
| :----:  | :-----:|:-----: |
|   $1\sim 2$    |  $n=1$    |无  |
|   $3\sim 4$    |$n=2$  | 无 |
|   $5\sim 6$     | $n=3$  | 无| 
|  $7\sim 8$    |  $n=4$ | 无|
|   $9\sim 10$    |$n=5$  |无 |
|   $11\sim 12$     | $n\le 8$  | 所有手牌花色相同| 
|  $13\sim 14$    |  $n\le 8$ | 所有手牌数字不同|
|   $15\sim 20$    | $n\le 8$  |无 |





# U423287 排列

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

注：本题采用 `Special Judge` 对选手答案进行自定义判题。为了方便大家练习，我们将给出判题器报错的具体信息所代表的含义，练习过程中将鼠标移动至评测点的 UI 即可浮现对应的报错信息。

- `invalid output.` 非法输出，输出存在非整数或者超出 `int` 范围的整数。
- `invalid permutation.` 当判定有解输出序列时，不是一个 $1\sim n$ 的合法排列。
- `invalid testcase number.` 输出的数据组数非法。
- `wrong answer on the existence of solution.` 一组数据的有解/无解性质判断错误。
- `wrong answer on the size of permutation.` 当判定有解输出序列时，输出序列规模有误。
- `wrong answer on the permutation.` 当判定有解输出序列时，输出序列不满足输入的条件限制。
- `Answer correct.` 本组测试点正确。

## 题目描述

现在有一个排列 $a$ （ $1$ 到 $n$ 只出现了一次的数组），$k$ 个限制，每个限制有两个数字 $p$ 和 $x$ ，表示下标小于等于 $p$ 的数字中，值小于等于 $a_p$ 的恰好为 $x$ 个。即满足 $a_i\le a_p$ 且 $i\le p$ 的 $i$ 的数量恰好是 $x$ 个。

现在请你构造出这个 $a$ 数组，如果有多种构造方式，输出任意一种，如果构造不出来，请输出 $-1$ 。

## 输入格式

从标准输入读入数据。

第一行一个整数 $T$ ，表示有 $T$ 组数据。

之后每组数据第一行两个整数 $n,k$ ，表示有 $n$ 个数字，$k$ 组限制。

之后的 $k$ 行，每一行有两个数字 $p$ 和 $x$ ，表示下标小于等于 $p$ 的数字中，值小于等于 $a_p$ 的恰好为 $x$ 个，保证每个限制条件中的 $p$ 各不相同。

## 输出格式

输出到标准输出。

输出共计 $T$ 行。对于每组数据，输出 $n$ 个整数，代表你构造出来的 $a$ 数组。如果有多种构造方式，输出任意一种。如果构造不出来，请输出 $-1$ 。

## 输入输出样例 #1

### 输入 #1

```
2
5 2
2 2
5 4
10 1
1 10
```

### 输出 #1

```
1 2 5 3 4
-1
```

## 说明/提示

### 样例 1 解释

在有解的情况下，输出任意一种正确答案即可。对于样例 1 当中的第一组数据，输出 `1 2 5 3 4` 或 `1 2 3 5 4` 或其他满足两组限制条件的排列都是正确的。第二组数据无论如何也无法满足要求，故输出一行 `-1` 即可。

### 数据范围

对于全部数据，保证 $1\le k,p\le n,~1\le x,n\le 10^5,~\sum n\le 5\times 10^5$ 。

| 子任务编号 | 分值  | 数据范围  | 其他限制 |
| :----:  | :---: | :-----:|:-----: |
|   1 | 20  |  $T\le 10,~n\le 9$    |无  |
|   2 | 30 |$T\le 10,~n\le 2000$  | 无 |
|   3  | 10  | $\sum n\le 5\times 10^5$  | $p\le x$| 
|  4  | 40   |  $\sum n\le 5\times 10^5$ | 无|