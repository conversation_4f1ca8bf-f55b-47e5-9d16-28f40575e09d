# 清华考研机试 2023
# U412534 公司

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

给定一个有 $n$ 个雇员的初创公司，雇员从 $1$ 到 $n$ 编号，编号为 $i$ 的人有一个固定的薪资 $a_i$。最初所有人都不知道公司里其他员工的薪资。

某一天由于公司数据库发生问题，泄露了 $m$ 条数据，导致有一部分人知道了其他部分人的薪资。其中对于编号为 $i$ 的雇员，设他所了解到的人的平均薪资为 $v_i$ （如果有多条重复的数据，那么也会被计算多次），如果 $a_i<v_i$ 那么他就会萌生想要离职的想法。

当然如果一个人不了解其他人的薪资，那么他也不会萌生想要离职的想法。

给定所有 $n$ 个人的薪资 $a_i$，以及 $m$ 个数对 $(x_i,y_i)$ 表示编号为 $x_i$ 的雇员知道了编号为 $y_i$ 的雇员的薪资，问会有多少雇员萌生离职的想法。

## 输入格式

从标准输入读入数据。

输入的第一行包含两个正整数 $n,m$, 分别表示公司的人数和泄露的数据条数。

输入的第二行包含 $n$ 个正整数 $a_i$, 依次表示 $n$ 个人的薪资。

接下来 $m$ 行，每行包含两个正整数 $(x_i,y_i)$ 表示编号为 $x_i$ 的雇员知道了编号为 $y_i$ 雇员的薪资。

## 输出格式

输出到标准输出。

输出一个正整数表示对应的答案。

## 输入输出样例 #1

### 输入 #1

```
4 4
10 20 30 40
3 2
3 4
3 4
1 2
```

### 输出 #1

```
2
```

## 说明/提示

### 样例 1 解释

编号为 $1$ 和 $3$ 的雇员都会萌生离职的想法。

### 数据范围

本题共 $10$ 个测试点，每个测试点 $10$ 分。

对于所有的数据，保证：$3\le n\le 10^5,1\le m\le 2\times 10^5,1\le a_i\le 10^5,1\le x_i,y_i\le n$。

对于编号为 $1\sim 3$ 的测试点，保证：$n,m\le 100$。

对于编号为 $4\sim 6$ 的测试点，保证：$y_i=x_i+1$。

对于编号为 $7\sim 10$ 的测试点，无额外保证。


# U412641 任务调度

## 题目背景

**时间限制：** 3.0 秒

**空间限制：** 512 MB

## 题目描述

任务调度是计算机系统中一项重要的工作。今天你的任务，就是模拟一个计算机系统模型的任务调度过程，并给出相应操作的执行结果。

在这个模型中，不同任务按照一定顺序到来，等待被执行。任务处理机制需要维护任务的等待情况，并在相应的时机选择相应的任务进行执行。

不同的任务之间以编号进行区分，为方便起见，按照任务到来的顺序，由先到后编号为 $1,2,3,...$。每个任务都拥有一个重要程度 $a_i$，所有任务的重要程度两两不同。

在一般情况下，处理任务应当按照任务到来的先后顺序依次处理，也就是说任务等待应当形成一个队列。但考虑到不同任务的重要程度不同，这一原则可能被打破。具体而言，有如下几种操作：

- $1~a_i$：一个新的任务到来，其编号为先前出现过的最大任务编号 $+1$，其重要程度为 $a_i$，在任务等待队列中被安排至队列末尾。考虑到计算机内存限制，同一时刻正在等待的任务数量不能超过 $m$，因此如果当前已经有 $m$ 个任务在等待，则这一操作将出现错误。
- $2~a_i~x_i$：一个新的任务到来，其编号为先前出现过的最大任务编号 $+1$，其重要程度为 $a_i$，在任务等待队列中被安排至任务编号为 $x_i$ 的任务前面并紧挨任务 $x_i$ 的位置。如果当前已有 $m$ 个任务在等待，或任务 $x_i$ 当前不在等待队列中，这一操作将出现错误。
- $3$：任务处理机制将处理当前排在等待队列队首的任务，并将其从等待队列中移除。若当前等待队列为空，这一操作将出现错误。
- $4$：任务处理机制将处理当前等待队列中重要程度最大的任务，并将其从等待队列中移除。若当前等待队列为空，这一操作将出现错误。

除上述提到的错误情况外，操作均可以成功执行。

最开始，任务等待队列为空，接下来你需要处理 $n$ 个操作，每个操作形如上述几种之一。对于每个操作，你需要正确判断是否会出现错误，如果出现错误，需要输出一个 `ERR` ，并不予以执行（但对于操作 $1$ 和 $2$ 而言，仍会占用一个新的任务编号）；如果可以成功执行，则需要输出一个正整数，表示这次操作涉及到的任务编号，在操作 $1$ 和 $2$ 中表示新到来的任务编号，操作 $3$ 和 $4$ 中表示被处理的任务编号。

## 输入格式

从标准输入读入数据。

输入的第一行包含两个正整数 $n, m$，分别表示需要执行的操作个数和队伍的最大容量。

接下来 $n$ 行，每行按上述格式描述一个操作。

## 输出格式

输出到标准输出。

输出 $n$ 行，每行表示对应操作执行的结果，格式如上所述。

## 输入输出样例 #1

### 输入 #1

```
12 3
1 2
1 6
2 1 2
2 7 3
1 5
3
3
1 8
2 4 3
4
4
4
```

### 输出 #1

```
1
2
3
ERR
ERR
1
3
6
ERR
6
2
ERR
```

## 说明/提示

### 样例 1 解释

第 $4, 5$ 次操作均因等待队列已满而出现错误，第 $9$ 次操作因 $x_i$ 不存在于等待队列中而出现错误，第 $12$ 次操作因等待队列为空而出现错误。

### 子任务

对于全部的数据，保证：$1 \le n, m \le 5 \times 10^5,1 \le a_i, x_i \le n$，所有 $a_i$ 两两不同。

| 测试点编号  | $n \le$ | $m \le$ | 特殊条件 |
| :----:  | :-----: | :-----: | :------: |
|   $1\sim 2$     |  $200$   |   $200$   |    无    |
|   $3\sim 5$    | $3000$  |   $500$   |    无    |
|   $6\sim 7$     |  $5 \times 10^5$   | $100$  |    无    |
|   $8\sim 10$     | $5 \times 10^5$  | $5 \times 10^5$  |    没有操作 $2$ 和 $4$     |
|   $11\sim 13$     | $5 \times 10^5$  | $5 \times 10^5$  |    没有操作 $4$    |
|   $14\sim 16$     | $5 \times 10^5$  | $5 \times 10^5$  |    没有操作 $2$   |
|   $17\sim 20$     | $5 \times 10^5$  | $5 \times 10^5$  |    无    |


# U413065 集合 子任务 1~2

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）

**该提交为本题的 subtask 01 & 02 部分，满分为 $8$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

注：subtask 01 & 02 部分为了使用尽可能多的数据进行准确性评测，该评测的时限将由 4 秒改为 1 秒。

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。


# U414735 集合 子任务 3~4

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）


**该提交为本题的 subtask 03 & 04 部分，满分为 $14$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。


# U414737 集合 子任务 5

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）

**该提交为本题的 subtask 05 部分，满分为 $13$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。


# U414738 集合 子任务 6

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）

**该提交为本题的 subtask 06 部分，满分为 $14$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。


# U414739 集合 子任务 7

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）

**该提交为本题的 subtask 07 部分，满分为 $7$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。


# U414740 集合 子任务 8

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）

**该提交为本题的 subtask 08 部分，满分为 $6$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。


# U414741 集合 子任务 9

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）

**该提交为本题的 subtask 09 部分，满分为 $15$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。


# U414938 集合 子任务 10

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）

**该提交为本题的 subtask 10 部分，满分为 $13$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。


# U414939 集合 子任务 11

## 题目背景

**时间限制：** 4.0 秒

**空间限制：** ~~1024 MB~~ 512 MB

由于洛谷个人用户无法设置 512 MB 以上的空间限制，故本题空间限制设置在 512 MB。可以确定的是满分做法的空间占用**远达不到** 512 MB。

如果你的部分分正确做法**仅因为**超过 512 MB 且在 1024 MB 以内而获得 `Memory Limit Exceeded` 的反馈信息，请优化你的空间常数。（例 : 树上倍增算法在本题的数据范围上限下可以优化到 400 MB~450 MB 左右。此算法并不一定是可以拿到部分分的解法，仅用来举例。）

**该提交为本题的 subtask 11 部分，满分为 $10$ 分。各子任务的限制具体见最下方。各 subtask 评测链接如下，请各位自行计算总分数。**

- [subtask 01 & 02 提交链接](https://www.luogu.com.cn/problem/U413065)
- [subtask 03 & 04 提交链接](https://www.luogu.com.cn/problem/U414735)
- [subtask 05 提交链接](https://www.luogu.com.cn/problem/U414737)
- [subtask 06 提交链接](https://www.luogu.com.cn/problem/U414738)
- [subtask 07 提交链接](https://www.luogu.com.cn/problem/U414739)
- [subtask 08 提交链接](https://www.luogu.com.cn/problem/U414740)
- [subtask 09 提交链接](https://www.luogu.com.cn/problem/U414741)
- [subtask 10 提交链接](https://www.luogu.com.cn/problem/U414938)
- [subtask 11 提交链接](https://www.luogu.com.cn/problem/U414939)

## 题目描述

给定一棵 $n$ 个点的有根树 $T$，树的节点从 $1$ 到 $n$ 标号，$1$ 为根。每个点有两个整数值 $a_i,b_i$。

称一个点集 $S$ 是好的当且仅当其满足以下条件：

$\forall~ u, v\in S~(u\ne v)$ 满足 $u$ 是 $v$ 的祖先，$\exist~ x\not\in S,y\in S$，使得：

- $x$ 在 $u$ 到 $v$ 的路径上；
- $b_y \le b_x$。

给出 $q$ 组询问，每组询问给出正整数 $c,d$，找到一个好的点集 $S$，最大化 $c\times (\sum\limits_{u\in S}a_u)+d\times (\min\limits_{u\in S}b_u)$。你只需要给出这个最大值。当 $S$ 为空时，认为 $\min\limits_{u\in S} b_u=0$。

## 输入格式

从标准输入读入数据。

第一行两个整数 $n,q$，描述树的节点数和询问次数。

接下来 $n-1$ 行，每行两个整数 $u,v$，描述树的一条边。

接下来 $n$ 行，第 $i$ 行两个整数 $a_i,b_i$，描述节点 $i$ 的权值。

接下来 $q$ 行，每行两个整数 $c,d$，描述一组询问。

## 输出格式

输出到标准输出。

对于每组询问输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
3 4
1 2
1 3
1 -2
-2 1
-5 2
1 1
1 3
3 1
1 10
```

### 输出 #1

```
0
1
1
15
```

## 说明/提示

### 样例 1 解释

四组询问选择的集合依次是 $\emptyset,\{2\},\{1\},\{3\}$。

### 子任务

对于所有测试数据：

- $1\le n,q\le 3\times 10^5$；
- $1\le u\ne v\le n$, 保证给出的 $n-1$ 条边构成一棵树；
- $-10^4\le a_i\le 10^4,-10^9\le b_i\le 10^9$；
- $1\le c,d\le 10^8$。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n\le$ | $q\le$ | 特殊性质 |
| :----: | :-----: | :-----: | :------: | :----: |
| 1 | 3 | $5$ | $5$ | $\times$ |
| 2 | 5 | $10$ | $10$ | $\times$ |
| 3 | 5 | $300$ | $300$ | $\times$ |
| 4 | 9 | $3000$ | $3000$ | $\times$ |
| 5 | 13 | $3000$ | $3\times 10^5$ | $\times$ |
| 6 | 14 | $7\times 10^4$ | $200$ | $\checkmark$ |
| 7 | 7 | $7\times 10^4$ | $3\times 10^5$ | $\checkmark$ |
| 8 | 6 | $3\times 10^5$ | $3\times 10^5$ | $\checkmark$ |
| 9 | 15 | $7\times 10^4$ | $200$ | $\times$ |
| 10 | 13 | $7\times 10^4$ | $3\times 10^5$ | $\times$ |
| 11 | 10 | $3\times 10^5$ | $3\times 10^5$ | $\times$ |

特殊性质：$\forall~ 1\le i\le n-1$，$i$ 和 $i+1$ 有一条边。