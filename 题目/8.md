# 清华考研机试 2023 试机
# U412490 数数

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

注：本题数据的输入输出格式与原题进行了微调，将一组输入改成多组输入。我们有 10 个计入分数的测试点（每个测试点 10 分）以及 10 个不计入分数的测试点（每个测试点 0 分），只通过前 10 个测试点视为 100 分 `unaccepted` ，通过所有测试点视为 `accepted` 。

## 题目描述

对于每个数据点你需要处理 $T$ 组查询。每次查询输入 $n$ ，求长度为 $n$ 的字符串个数，要求：

- 每一位为 `1` , `2` 或 `3` ;
- 不得连续出现 3 个相同的数字。

## 输入格式

从标准输入读入数据。

输入的第一行包含一个正整数 $T$ ，表示共有 $T$ 组数据。

接下来 $T$ 行，每组数据占一行，分别包含一个正整数 $n$ 。

## 输出格式

输出到标准输出。

共输出 $T$ 行，第 $i$ 行对应第 $i$ 组询问所求的答案：

- 如果答案不超过 16 位数，则直接输出。
- 如果答案至少 17 位数，则先输出 `......` ，然后输出答案的最后 10 位。

## 输入输出样例 #1

### 输入 #1

```
3
4
28
51
```

### 输出 #1

```
66
1970947301376
......9614132224
```

## 说明/提示

### 样例解释

对于 $n=4$ , 符合条件的串有：

`1121`, `1122`, `1123`, `1131`, `1132`, `1133`, `1211`, `1212`, `1213`, `1221`, `1223`, `1231`, `1232`, `1233`, `1311`, `1312`, `1313`, `1321`, `1322`, `1323`, `1331`, `1332`, `2112`, `2113`, `2121`, `2122`, `2123`, `2131`, `2132`, `2133`, `2211`, `2212`, `2213`, `2231`, `2232`, `2233`, `2311`, `2312`, `2313`, `2321`, `2322`, `2323`, `2331`, `2332`, `3112`, `3113`, `3121`, `3122`, `3123`, `3131`, `3132`, `3133`, `3211`, `3212`, `3213`, `3221`, `3223`, `3231`, `3232`, `3233`, `3311`, `3312`, `3313`, `3321`, `3322`, `3323`。

### 数据范围

本题分两个 subtask ，每个 subtask 内部采用传统计分方式。

subtask 1 : 每个测试点 10 分，所有数据保证 $T=1$ 。对于其中 $40\%$ 的数据，保证答案不超过 16 位数。对于 $100\%$ 的数据，保证 $n\le 10^6$ 。

subtask 2 : 每个测试点 0 分，所有数据保证 $T=10^5, n\le 10^6$ 。



# U412495 粽子树

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

注 : 本题最后两个数据经过了刻意设计，在原题 1.0 秒的时限下会略微卡常。但是由于原题设置如此，故此题时空限制均不做改动。我们提供以下几种卡常思路：

- 使用更快的输入输出方法；
- 使用访问时间常数和空间占用常数更小的存储粽子树以及粽子种类的结构；
- 将搜索过程用栈进行迭代模拟，本题空间占用可以优化到空间限制的十分之一以内。

## 题目描述

小粽有一棵粽子树。这棵树有 $n$ 个结点，编号依次为 $1$ 到 $n$ ，根节点的编号为 $n$ 。这棵树的每个点都会结出一个粽子。第 $i$ 个点的粽子种类可以用一个整数 $a_i$ 表示。

小粽没事的时候喜欢爬树玩耍。这天，小粽想到了一个问题：对于任意的点 $i$ ，如何求出 $i$ 到根节点简单路径上不同粽子的种类数呢？

这个问题对小粽来说太难了，你能帮她算出来吗？

## 输入格式

从标准输入读入数据。

输入第一行为一个整数，表示树的节点数目。

接下来 $n-1$ 行，每行两个整数 $u,v$ ，表示树上编号为 $u,v$ 的两点之间存在一条边。

接下来一行输入 $n$ 个整数，第 $i$ 个数为 $a_i$ ，表示编号为 $i$ 的节点上结出的粽子的种类。

## 输出格式

输出到标准输出。

输出一行，包含 $n$ 个正整数，第 $i$ 个数表示编号为 $i$ 的点到根的路径上不同粽子的种类数。相邻两个数之间用一个空格分隔。

## 输入输出样例 #1

### 输入 #1

```
3
3 1
3 2
1 2 1
```

### 输出 #1

```
1 2 1
```

## 说明/提示

### 数据范围

对于 $20\%$ 的数据，$1\le n\le 10^2, 1\le a_i\le 10^2$ 。

对于 $50\%$ 的数据，$1\le n \le 10^3,1\le a_i\le 10^3$ 。

对于 $80\%$ 的数据，$1\le n\le 10^5,1\le a_i\le 10^5$ 。

对于 $100\%$ 的数据，$1\le n\le 10^6,-2147483648\le a_i\le 2147483647$ 。


# U412529 互质数

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

有 $n$ 个数字，$a_1,a_2,...,a_n$ 。有一个集合，刚开始集合为空。然后有一种操作每次向集合中加入一个数字或者删除一个数字。每次操作给出一个下标 $x(1\le x\le n)$ ，如果 $a_x$ 已经在集合中，那么就删除 $a_x$ ；否则就加入 $a_x$ 。

问每次操作之后集合中互质的数字有多少对。

注意，集合中可以有重复的数字，两个数字不同当且仅当他们的下标不同。

比如有两个数字 $a_1=a_2=1$ 。那么在经过两次操作 $1,2$ 之后，集合内存在两个 $1$ ，有一对互质。

## 输入格式

从标准输入读入数据。

第一行包含两个整数 $n$ 和 $q$ 。表示数字的种类和查询数目。

第二行有 $n$ 个以空格分开的整数 $a_1,a_2,...,a_n$ ，分别表示 $n$ 个数字。

接下来 $q$ 行，每行一个整数 $x$ ，表示每次操作的下标。

## 输出格式

输出到标准输出。

对于每一个查询，输出当前集合中互质的数字有多少对。

## 输入输出样例 #1

### 输入 #1

```
5 6
1 2 3 4 6
1
2
3
4
5
1
```

### 输出 #1

```
0
1
3
5
6
2
```

## 说明/提示

### 数据规模约定

对于 $30\%$ 的数据，$1\le n\le 100,1\le q\le 1000$ 。

对于所有数据，$1\le n\le 10^5,1\le q\le 10^5,1\le a_i\le 5\times 10^5$ 。