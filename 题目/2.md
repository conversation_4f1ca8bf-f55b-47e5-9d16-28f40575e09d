# 清华考研机试 2025 试机
# U560763 Movie

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

Ecrade_ 正在观看一个影片。

给定影片的时长，并假设该影片以恒定的 $x$ 帧每秒的帧率播放，问该影片一共显示了几帧？

## 输入格式

从标准输入读入数据。

第一行一个整数 $T$，表示测试数据组数。

对于每组测试数据，一行一个形如 `AB:CD:EF` 的字符串和一个整数 $x$，表示该影片时长为 `AB` 时 `CD` 分 `EF` 秒，帧率为 $x$ 帧每秒。

## 输出格式

输出到标准输出。

对于每组测试数据，输出一行一个整数表示答案。

## 输入输出样例 #1

### 输入 #1

```
2
00:00:01 1
06:06:06 120
```

### 输出 #1

```
1
2635920
```

## 说明/提示

### 样例 1 解释

- 对于第一组测试数据，可求得该影片共 $1$ 秒，故一共显示了 $1\times 1=1$ 帧。
- 对于第二组测试数据，可求得该影片共 $21966$ 秒，故一共显示了 $21966\times 120=2635920$ 帧。

### 数据规模与约定

**本题采用捆绑测试。**

- 子任务 1（20 分）：$A=B=C=D=E=F=0$。
- 子任务 2（20 分）：$A=B=C=D=0$。
- 子任务 3（20 分）：$A=B=0$。
- 子任务 4（20 分）：$x=1$。
- 子任务 5（20 分）：无特殊限制。

对于 $100\%$ 的数据，$1\le T\le 100,~0\le A,B,D,F\le 9,~0\le C,E\le 5,~1\le x\le 240$。


# U560764 Binary Matrix

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

称一个矩阵为二进制矩阵，当且仅当该矩阵中所有元素为 $0$ 或 $1$。

令 $S$ 为所有同时满足如下两个条件的 $n\times m$ 的二进制矩阵 $B=\{b_{i,j}\}$ 构成的集合：

- $B$ 每行中所有数的异或和为 $0$，即 $\forall ~1\le i\le n,~\mathop{\oplus}\limits_{j=1}^m b_{i,j}=0$；
- $B$ 每列中所有数的异或和为 $0$，即 $\forall ~1\le i\le m,~\mathop{\oplus}\limits_{i=1}^n b_{i,j}=0$。

Ecrade_ 有一个 $n\times m$ 的二进制矩阵 $A=\{a_{i,j}\}$，他定义一个 $n\times m$ 的二进制矩阵 $B=\{b_{i,j}\}$ 的权值为 $a_{i,j}$ 与 $b_{i,j}$ 中不同数的个数，即 $\sum\limits_{i=1}^n\sum\limits_{j=1}^m[a_{i,j}\ne b_{i,j}]$。

Ecrade_ 想知道，$S$ 中矩阵权值的最小值为多少？

## 输入格式

从标准输入读入数据。

第一行一个整数 $T$，表示测试数据组数。

对于每组测试数据：

- 第一行两个整数 $n,m$。
- 后 $n$ 行每行一个长为 $m$ 的数字串，其中第 $i+1$ 行第 $j$ 个字符表示 $a_{i,j}$。

## 输出格式

输出到标准输出。

对于每组测试数据，输出一行一个整数 $x$，表示 $S$ 中矩阵权值的最小值。

## 输入输出样例 #1

### 输入 #1

```
2
3 3
010
101
010
3 3
000
000
000
```

### 输出 #1

```
2
0
```

## 说明/提示

### 样例 1 解释

- 对于第一组测试数据，$S$ 中权值最小的一个矩阵为 $\begin{pmatrix}1&1&0\\1&0&1\\0&1&1\end{pmatrix}$；
- 对于第二组测试数据，$S$ 中权值最小的一个矩阵为 $\begin{pmatrix}0&0&0\\0&0&0\\0&0&0\end{pmatrix}$。

### 数据规模与约定

**本题采用捆绑测试。**

- 子任务 1（10 分）：$n=1$。
- 子任务 2（10 分）：$n,m\le 2$。
- 子任务 3（20 分）：$T\le 10,~n,m\le 4$。
- 子任务 4（20 分）：$n=2$。
- 子任务 5（40 分）：无特殊限制。

对于 $100\%$ 的数据，$1\le T\le 2\times 10^5,~1\le n,m\le 10^3,~1\le \sum n\times m\le 10^6,~a_{i,j}\in\{0,1\}$。


# U560765 Floor of Ceil

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

Ecrade_ 有一个整数 $x$，他会对其进行一些操作。

共有如下两种操作：

- 操作 1：令 $x \leftarrow \lfloor \frac{x}{2} \rfloor$。
- 操作 2：令 $x \leftarrow \lceil \frac{x}{2} \rceil$。

Ecrade_ 会以任意顺序进行恰好 $n$ 次操作 1 和 $m$ 次操作 2，他想知道在这 $n+m$ 次操作后，$x$ 的值最小和最大分别是多少。

## 输入格式

从标准输入读入数据。

第一行一个整数 $T$，表示测试数据组数。

对于每组测试数据，一行三个整数 $x,n,m$。

## 输出格式

输出到标准输出。

对于每组测试数据，输出一行两个整数，分别表示在 $n+m$ 次操作后，$x$ 的值最小和最大分别是多少。

## 输入输出样例 #1

### 输入 #1

```
4
12 1 2
12 1 1
12 0 0
12 1000000000 1000000000
```

### 输出 #1

```
1 2
3 3
12 12
0 0
```

## 说明/提示

### 样例 1 解释

对于第一组测试数据：

- 依次进行操作 2,2,1，则 $x$ 会变为 $12\to 6\to 3\to 1$，可以证明这是 $x$ 可变为的最小值。
- 依次进行操作 2,1,2，则 $x$ 会变为 $12\to 6\to 3\to 2$，可以证明这是 $x$ 可变为的最大值。

### 数据规模与约定

**本题采用捆绑测试。**

- 子任务 1（10 分）：$x=0$。
- 子任务 2（10 分）：$m=0$。
- 子任务 3（10 分）：$n=0$。
- 子任务 4（20 分）：$T,n,m\le 10$。
- 子任务 5（20 分）：$x\le 10^5,~n,m\le 10$。
- 子任务 6（30 分）：无特殊限制。

对于 $100\%$ 的数据，$1\le T\le 2\times 10^5,~0\le x,n,m\le 10^9$。