# 清华考研机试 2025
# T590312 见

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

给一个 $n\times n$ 的网格图，从上到下第 $i$ 行，从左到右第 $j$ 列有一个高度为 $h_{i,j}$ 的柱子。

定义 $l_i$ 为：从第 $i$ 行的左侧看去，能看到多少根柱子；

- 如果一个更远的柱子的高度**不超过**一个更近的柱子的高度，那么其不会被看到。

定义 $r_i$ 为：从第 $i$ 行的右侧看去，能看到多少根柱子；

定义 $u_i$ 为：从第 $i$ 列的上侧看去，能看到多少根柱子；

定义 $d_i$ 为：从第 $i$ 列的下侧看去，能看到多少根柱子；

请对每个 $i\in [1,n]$，求出 $l_i,r_i,u_i,d_i$。

## 输入格式

从标准输入读入数据。

第一行，一个正整数 $n$；

接下来 $n$ 行，每行 $n$ 个正整数，其中第 $i$ 行第 $j$ 列为 $h_{i,j}$。

## 输出格式

输出到标准输出。

$n$ 行，每行四个正整数 $l_i,r_i,u_i,d_i$，意义如上所述。

## 输入输出样例 #1

### 输入 #1

```
4
3 4 2 1
2 1 4 3
4 3 1 2
1 2 3 4
```

### 输出 #1

```
2 3 2 2
2 2 1 3
1 3 2 2
4 1 3 1
```

## 输入输出样例 #2

### 输入 #2

```
10
7 2 9 1 5 8 3 6 4 10
4 6 1 7 9 2 10 3 5 8
8 3 5 10 1 4 6 7 9 2
2 9 7 3 6 10 5 8 1 4
10 1 3 4 8 5 2 9 7 6
5 8 10 6 2 7 4 1 3 9
1 4 6 8 3 9 7 10 2 5
6 10 2 5 7 1 9 4 8 3
3 5 8 9 10 6 1 2 4 7
9 7 4 2 5 3 8 10 6 1
```

### 输出 #2

```
3 1 3 2
5 2 4 2
2 3 2 3
3 3 3 3
1 4 3 2
3 2 2 4
6 2 2 3
2 4 5 1
5 2 3 3
2 3 1 4
```

## 说明/提示

### 样例 1 解释

![](https://www.smqyoj.com/p/113/file/1.png)

### 数据范围

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

对于 $10\%$ 的数据，$n=1$；

对于 $20\%$ 的数据，$n\le 2$；

对于 $30\%$ 的数据，$n\le 3$；

对于 $40\%$ 的数据，$n\le 4$；

对于 $50\%$ 的数据，$n\le 5$；

对于 $60\%$ 的数据，$n\le 6$；

对于 $80\%$ 的数据，$n\le 20$；

对于 $100\%$ 的数据，$1\le n\le 100,~1\le h_{i,j}\le n$。








# T590313 Bloxorz

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

## 题目描述

Bloxorz 是一款风靡全球的益智游戏，以其独特的方块操控和复杂的关卡设计而著称。在这个游戏中，玩家需要使用键盘方向键来控制方块的移动，使其到达指定的终点。

![Bloxorz](https://7265-release-5gxeaot882cf5b43-1312410623.tcb.qcloud.la/manager_data/image/1742828858008QWE6o.png)

具体的游戏规则如下：给定一个平面网格区域，每一格为空地，地面或玻璃。在地面上放置着一块 $1\times 1\times 2$ 的长方体木块，可以**竖放**在**一格地面**上，或**平放**在**相邻的两格地面、或相邻的两格玻璃、或相邻的一格地面和一格玻璃上**。每次操作可以选择四个方向中的一个，将木块向该方向滚动 $90\degree$。游戏的目标是以最少的移动次数将盒子**竖放**在唯一的目标地砖上。初始位置和目标位置的区域类型均为地面，而非玻璃或者空地。

给定一个 $n\times m$ 的平面网格，其中 `#` 表示空地，`.` 表示地面，`E` 表示玻璃，`X` 表示初始位置，`O` 表示目标位置，求达成游戏目标的最小操作数。

## 输入格式

从标准输入读入数据。

**本题包含多组测试数据。**

输入的第一行包含一个正整数 $T$，表示测试数据组数。

对于每组测试数据：

输入的第一行包含两个正整数 $n,m$，表示平面网格的大小。

输入的第 $i+1~(1\le i\le n)$ 行包含一个长度为 $m$ 的仅由 `#`、`.`、`E`、`X`、`O` 组成的字符串，描述给定的游戏局面，具体含义如【题目描述】中所示。

## 输出格式

输出到标准输出。

对于每组测试数据：

输出一行一个整数表示到达成游戏目标的最小操作数。特别地，若游戏目标无法达成，则输出 $-1$。

## 输入输出样例 #1

### 输入 #1

```
1
7 7
#######
#..X###
#..##O#
#....E#
#....E#
#.....#
#######
```

### 输出 #1

```
10
```

## 说明/提示

### 子任务

对于所有测试数据，保证 $1\le T\le 5,~3\le n,m\le 500$，保证每个游戏局面都有恰好一个 `O` 与恰好一个 `X` 或相邻的两个 `X`。保证给定的游戏局面中的第一行、第一列、最后一行、最后一列均为 `#`。

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号 | 分值 | $n,m\le$ | 
|:---:|:---:| :---: |
| 1 | 30 | $20$ |
| 2 | 20 | $50$ |
| 3 | 20 | $300$ |
| 4 | 30 | $500$ |







# T590314 任务分配

## 题目背景

**时间限制：** 1.0 秒

**空间限制：** 512 MB

本题的额外子任务见 [link](https://www.luogu.com.cn/problem/T645450)。

## 题目描述

给定一棵 $n$ 个点的有根树，其中节点 $1$ 为根，节点 $i~(2\le i\le n)$ 的父亲为 $f_i$。

有 $n$ 个任务，第 $i$ 个任务的**耗时**为 $t_i$。你需要将这 $n$ 个任务分配给 $n$ 个节点，满足每个节点恰好完成一个任务。于此同时，每个节点均有一个**限制**，节点 $i~(1\le i\le n)$ 的限制为 $w_i$，要求在节点 $i$ 的**子树内的所有任务的耗时均不能超过 $w_i$**。

现在，你可以选择一个节点，并将其的**限制增加** $k$，其中 $k$ 为任意非负整数，你需要求出最小的 $k$，使得在增大一个节点的限制后，存在一种任务分配的方案满足所有限制，**保证存在至少一种增加限制的方案使得存在一种任务分配的方案满足所有限制**。

## 输入格式

从标准输入读入数据。

输入的第一行包含一个正整数 $n$，表示树的点数与任务的个数。

输入的第二行包含 $n-1$ 个正整数 $f_2,...,f_n$，表示 $2\sim n$ 号节点的父亲。

输入的第三行包含 $n$ 个正整数 $t_1,t_2,...,t_n$，表示每个任务的耗时。

输入的第四行包含 $n$ 个正整数 $w_1,w_2,...,w_n$，表示每个节点的限制。

## 输出格式

输出到标准输出。

输出一行一个非负整数 $k$，表示限制增量的最小值。

## 输入输出样例 #1

### 输入 #1

```
6
1 2 2 1 5
1 2 2 3 5 6
6 1 3 3 5 1
```

### 输出 #1

```
2
```

## 说明/提示

### 样例 1 解释

一种可行的方案为：将节点 $2$ 的限制增加 $2$ 后，将耗时为 $6,3,2,2,5,1$ 的任务依次分配给节点 $1\sim 6$。

### 子任务

对于所有测试数据，保证 $1\le n\le 10^5,~1\le f_i<i,~1\le t_1\le t_2\le ...\le t_n\le n,~1\le w_i\le n$。**保证存在至少一种增加限制的方案使得存在一种任务分配的方案满足所有限制。**

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号  | 分值 | $n \le$ | 特殊性质 |
| :----:  | :-----: | :------: | :------: |
|   1    |  15 | $6$  |    无    |
|   2  | 15 | $100$    |    无    |
|   3     | 15 |  $10^3$   |    无    |
|   4     | 15 | $10^5$   |   $f_i=i-1$     |
|   5    | 15 | $10^5$ |    $f_i=1$    |
|   6     | 25 |  $10^5$   |    无    |





# T645450 任务分配 子任务 7~9

## 题目背景

**时间限制：** ~~1.0 秒~~ 0.5 秒

**空间限制：** ~~1024 MB~~ 512 MB

请注意，在洛谷提交时不要在代码中引用 `task.h`。

本题只允许使用 C++17 或者 C++20 进行提交。

如果你的实现无法在 512 MB 的空间实现完成，请前往[此处](https://www.smqyoj.com/p/THU20252C2)提交。但是 std 保证可以在此空间限制下通过。

本评测为[任务分配](https://www.luogu.com.cn/problem/T590314)一题的额外子任务，附加分数为 30 分。

## 题目描述

给定一棵 $n$ 个点的有根树，其中节点 $1$ 为根，节点 $i~(2\le i\le n)$ 的父亲为 $f_i$。

有 $n$ 个任务，第 $i$ 个任务的**耗时**为 $t_i$。你需要将这 $n$ 个任务分配给 $n$ 个节点，满足每个节点恰好完成一个任务。于此同时，每个节点均有一个**限制**，节点 $i~(1\le i\le n)$ 的限制为 $w_i$，要求在节点 $i$ 的**子树内的所有任务的耗时均不能超过 $w_i$**。

现在，你可以选择一个节点，并将其的**限制增加** $k$，其中 $k$ 为任意非负整数，你需要求出最小的 $k$，使得在增大一个节点的限制后，存在一种任务分配的方案满足所有限制，**保证存在至少一种增加限制的方案使得存在一种任务分配的方案满足所有限制**。

## 输入格式

本提交窗口**无需从标准输入读入数据。**

## 输出格式

本提交窗口**无需输出到标准输出。**

## 输入输出样例 #1

### 输入 #1

```
6
1 2 2 1 5
1 2 2 3 5 6
6 1 3 3 5 1
```

### 输出 #1

```
2
```

## 输入输出样例 #2

### 输入 #2

```
499998 55815863 1
```

### 输出 #2

```
381079
```

## 输入输出样例 #3

### 输入 #3

```
3000000 393087878 1
```

### 输出 #3

```
354159
```

## 说明/提示

## 交互方式

这是一道函数式交互题，不需要选手考虑输入输出，也不要从标准输入读入数据，或将任何内容输出到标准输出，否则会影响判题。

你需要实现以下函数。

```c++
int solve(const std::vector<int>& f, const std::vector<int>& t, const std::vector<int>& w);
```

请注意以下几点：

- 交互库**没有**包含万能头，如使用其他库，需要自行引入；
- 交互库**没有**引入 `using namespace std`；
- 函数传入的 `vector` **不可被修改**。

其中传入的三个 `vector` 的规模均为 $n+1$，其中：

- $f_2,\cdots f_n$ 放置在动态数组 `f` 当中秩为 $2,3,\cdots, n$ 的位置，表示 $2\sim n$ 号节点的父亲；
-  $t_1,\cdots t_n$ 放置在动态数组 `t` 当中秩为 $1,2,\cdots, n$ 的位置，表示每个任务的耗时；
-  $w_1,\cdots w_n$ 放置在动态数组 `w` 当中秩为 $1,2,\cdots, n$ 的位置，表示每个节点的限制；
- 动态数组的开头元素秩为 $0$，其余未填写内容的数组元素值均为 $0$。

返回的整数即为限制增量的最小值。

## 交互库实例

具体请见附加文件区。需要注意的是，该白盒交互库**并非实际评测使用的交互库。**

如果你本地的实现代码为 `main.cc`，则将交互库放在同一目录下，输入以下 Linux 命令行即可运行：

```plain
g++ main.cc interactor.cc -Wall -std=c++20 -o foo -lm -O2 -I/include
```

### 样例 1 解释

一种可行的方案为：将节点 $2$ 的限制增加 $2$ 后，将耗时为 $6,3,2,2,5,1$ 的任务依次分配给节点 $1\sim 6$。

请注意，本样例**并未使用上述提供的白盒交互库**。

### 样例 2 解释

本样例有 $n\le 5\times 10^5$，样例输入输出均遵循上述白盒交互库获得。

### 样例 3 解释

本样例有 $n\le 3\times 10^6$，样例输入输出均遵循上述白盒交互库获得。

### 子任务

对于所有测试数据，保证 $1\le n\le 1.5\times 10^7,~1\le f_i<i,~1\le t_1\le t_2\le ...\le t_n\le n,~1\le w_i\le n$。**保证存在至少一种增加限制的方案使得存在一种任务分配的方案满足所有限制。**

**本题采用捆绑测试，你只有通过一个子任务中的所有测试点才能得到该子任务的分数。**

| 子任务编号  | 分值 | $n \le$ | 特殊性质 |
| :----:  | :-----: | :------: | :------: |
|   7    |  10 | $5\times 10^5$  |    无    |
|   8  | 10 | $3\times 10^6$    |    无    |
|   9  | 10 | $1.5\times 10^7$    |    无    |

### 提示

在子任务 9 下，黑盒交互库至多需要 500ms 生成输入数据，留给选手解决问题的时限实际只有 500ms。